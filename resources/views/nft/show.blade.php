@php use Catchain\Ton\Address\Address; @endphp
@extends('components.layouts.app')
@section('body')
    <div class="container mx-auto px-4 py-8 max-w-7xl text-white">
        <!-- Верхний блок с NFT и информацией -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- Изображение NFT -->
            <div class="bg-white/10 backdrop-blur-lg rounded-xl p-4 shadow-lg overflow-hidden">
                <img
                    src="{{$nftStates['metadata'][$nftStates['wallets'][0]['address']]['token_info'][0]['extra']['_image_big'] ?? domainToImage($nftStates['metadata'][$nftStates['wallets'][0]['address']]['token_info'][0]['extra']['domain']) ??'N / A'}}"
                    alt="NFT Image"
                    class="w-full h-auto rounded-lg object-cover aspect-square">
            </div>

            <!-- Информация о NFT -->
            <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 shadow-lg lg:col-span-2">
                <div class="flex flex-col h-full justify-between">
                    <div>
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center gap-2">
                                <h1 class="text-2xl md:text-3xl font-bold">{{$nftStates['metadata'][$nftStates['wallets'][0]['address']]['token_info'][0]['name'] ?? $nftStates['metadata'][$nftStates['wallets'][0]['address']]['token_info'][0]['extra']['domain'] ?? ' N / A'}}</h1>

                                <!-- Кнопка копирования адреса NFT -->
                                <div x-data="{
                                    copiedToClipboard: false,
                                    copyToClipboard() {
                                        navigator.clipboard
                                            .writeText(this.$refs.targetText.textContent.trim())
                                            .then(() => {
                                                this.copiedToClipboard = true;
                                                window.dispatchEvent(new CustomEvent('notify', {
                                                    detail: {
                                                        variant: 'success',
                                                        title: 'Успех!',
                                                        message: 'Адрес NFT скопирован'
                                                    }
                                                }));
                                                setTimeout(() => { this.copiedToClipboard = false; }, 2000);
                                            });
                                    }
                                }" class="flex items-center">
                                    <span x-ref="targetText"
                                          class="hidden">{{Address::parse($nftStates['wallets'][0]['address'])->toTonscanFormat()}}</span>
                                    <button
                                        class="rounded-full w-fit p-1 text-neutral-600/75 hover:bg-neutral-950/10 hover:text-neutral-600 focus:outline-hidden focus-visible:text-neutral-600 focus-visible:outline focus-visible:outline-offset-0 focus-visible:outline-black active:bg-neutral-950/5 active:-outline-offset-2 dark:text-neutral-300/75 dark:hover:bg-white/10 dark:hover:text-neutral-300 dark:focus-visible:text-neutral-300 dark:focus-visible:outline-white dark:active:bg-white/5"
                                        title="Копировать адрес NFT"
                                        aria-label="Копировать адрес NFT"
                                        x-on:click="copyToClipboard()"
                                    >
                                        <span class="sr-only"
                                              x-text="copiedToClipboard ? 'скопировано' : 'скопировать адрес'"></span>
                                        <svg x-show="!copiedToClipboard" xmlns="http://www.w3.org/2000/svg"
                                             viewBox="0 0 20 20" fill="currentColor" class="size-5" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                  d="M13.887 3.182c.396.037.79.08 1.183.128C16.194 3.45 17 4.414 17 5.517V16.75A2.25 2.25 0 0 1 14.75 19h-9.5A2.25 2.25 0 0 1 3 16.75V5.517c0-1.103.806-2.068 1.93-2.207.393-.048.787-.09 1.183-.128A3.001 3.001 0 0 1 9 1h2c1.373 0 2.531.923 2.887 2.182ZM7.5 4A1.5 1.5 0 0 1 9 2.5h2A1.5 1.5 0 0 1 12.5 4v.5h-5V4Z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                        <svg x-show="copiedToClipboard" xmlns="http://www.w3.org/2000/svg"
                                             viewBox="0 0 16 16" fill="currentColor" class="size-4 fill-green-500">
                                            <path fill-rule="evenodd"
                                                  d="M11.986 3H12a2 2 0 0 1 2 2v6a2 2 0 0 1-1.5 1.937V7A2.5 2.5 0 0 0 10 4.5H4.063A2 2 0 0 1 6 3h.014A2.25 2.25 0 0 1 8.25 1h1.5a2.25 2.25 0 0 1 2.236 2ZM10.5 4v-.75a.75.75 0 0 0-.75-.75h-1.5a.75.75 0 0 0-.75.75V4h3Z"
                                                  clip-rule="evenodd"/>
                                            <path fill-rule="evenodd"
                                                  d="M2 7a1 1 0 0 1 1-1h7a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7Zm6.585 1.08a.75.75 0 0 1 .336 1.005l-1.75 3.5a.75.75 0 0 1-1.16.234l-1.75-1.5a.75.75 0 0 1 .977-1.139l1.02.875 1.321-2.64a.75.75 0 0 1 1.006-.336Z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <p class="text-white/70 mb-6">{{$nftStates['metadata'][$nftStates['wallets'][0]['address']]['token_info'][0]['description'] ?? 'N / A'}}</p>

                        <!-- Атрибуты NFT -->
                        <div class="mb-6">
                            <h2 class="text-lg font-semibold mb-2">Атрибуты</h2>
                            <div class="flex flex-wrap gap-2">
                                @foreach($nftStates['metadata'][$nftStates['wallets'][0]['address']]['token_info'][0]['extra']['attributes'] ?? [] as $attribute)
                                    <div class="badge badge-primary badge-outline">
                                        <span
                                            class="font-medium">{{ $attribute['trait_type'] }}:</span> {{ $attribute['value'] }}
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <!-- Детали NFT -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-auto">
                        <div class="bg-white/5 p-3 rounded-lg">
                            <p class="text-xs text-white/50 mb-1">Создатель</p>
                            <div class="flex items-center gap-2">
                                <div class="avatar">
                                    <div class="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                             class="w-4 h-4 text-primary">
                                            <path fill-rule="evenodd"
                                                  d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                </div>
                                @if(isset($nftItems['nft_items'][0]['owner_address']))
                                    <a href="{{route('search', ['address' => Address::parse($nftItems['nft_items'][0]['owner_address'])->toTonscanFormat()])}}"
                                       class="text-sm text-white/80 hover:text-white hover:underline truncate">
                                        {{shortMiddle(Address::parse($nftItems['nft_items'][0]['owner_address'])->toTonscanFormat())}}
                                    </a>
                                @endif
                            </div>
                        </div>
                        <div class="bg-white/5 p-3 rounded-lg">
                            <p class="text-xs text-white/50 mb-1">Коллекция</p>
                            <div class="flex items-center gap-2">
                                <div class="avatar">
                                    <div class="w-6 h-6 rounded-full">
                                        <img
                                            src="{{ $nftItems['metadata'][$nftItems['nft_items'][0]['collection_address']]['token_info'][0]['extra']['_image_small'] ?? 'https://dns-image.mytonwallet.org/img?d=' . domainToImage($nftStates['metadata'][$nftStates['wallets'][0]['address']]['token_info'][0]['extra']['domain']) ??'https://via.placeholder.com/24' }}"
                                            alt="Collection"/>
                                    </div>
                                </div>
                                @if(isset($nftItems['nft_items'][0]['collection_address']))
                                    <a href="{{route('search', ['address' => Address::parse($nftItems['nft_items'][0]['collection_address'])->toTonscanFormat()])}}"
                                       class="text-sm text-white/80 hover:text-white hover:underline truncate">
                                        {{$nftItems['metadata'][$nftItems['nft_items'][0]['collection_address']]['token_info'][0]['name'] ?? 'N / A'}}
                                    </a>
                                @endif
                            </div>
                        </div>

                        <div class="bg-white/5 p-3 rounded-lg">
                            <p class="text-xs text-white/50 mb-1">Metadata</p>
                            <a class="text-sm text-white/80 hover:text-white hover:underline flex items-center gap-2"
                               href="{{$nftStates['metadata'][$nftStates['wallets'][0]['address']]['token_info'][0]['extra']['uri'] ?? 'N / A'}}"
                               target="_blank">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                     class="w-4 h-4">
                                    <path fill-rule="evenodd"
                                          d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm4.28 10.28a.75.75 0 000-1.06l-3-3a.75.75 0 10-1.06 1.06l1.72 1.72H8.25a.75.75 0 000 1.5h5.69l-1.72 1.72a.75.75 0 101.06 1.06l3-3z"
                                          clip-rule="evenodd"/>
                                </svg>
                                {{$nftStates['metadata'][$nftStates['wallets'][0]['address']]['token_info'][0]['extra']['content_url'] ?? 'N / A'}}
                            </a>
                        </div>

                        <div class="bg-white/5 p-3 rounded-lg">
                            <p class="text-xs text-white/50 mb-1">Баланс</p>
                            <p class="text-sm text-white/80">{{ $nftStates['wallets'][0]['balance'] / 1000000000 }}
                                TON</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Блок с историей транзакций -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 shadow-lg mb-6">
            <h2 class="text-xl font-bold mb-4 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                    <path fill-rule="evenodd"
                          d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 000-1.5h-3.75V6z"
                          clip-rule="evenodd"/>
                </svg>
                История транзакций
            </h2>
            @include('wallet.parts.history')
        </div>
    </div>
@endsection
