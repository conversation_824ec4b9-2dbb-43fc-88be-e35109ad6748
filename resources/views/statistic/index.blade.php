@php use Carbon\Carbon; @endphp
@extends('components.layouts.app')
@section('body')
    <div class="container mx-auto px-4 py-8 max-w-7xl text-white">
        <!-- Заголовок страницы -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 mb-6 shadow-lg">
            <div class="flex items-center justify-between mb-4">
                <h1 class="text-2xl md:text-3xl font-bold flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                         class="w-8 h-8 text-primary">
                        <path
                            d="M18.375 2.25c-1.035 0-1.875.84-1.875 1.875v15.75c0 1.035.84 1.875 1.875 1.875h.75c1.035 0 1.875-.84 1.875-1.875V4.125c0-1.036-.84-1.875-1.875-1.875h-.75zM9.75 8.625c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-.75a1.875 1.875 0 01-1.875-1.875V8.625zM3 13.125c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v6.75c0 1.035-.84 1.875-1.875 1.875h-.75A1.875 1.875 0 013 19.875v-6.75z"/>
                    </svg>
                    Статистика TON Blockchain
                </h1>
                <div class="text-sm text-white/70">
                    Обновлено: {{ Carbon::now()->format('d.m.Y H:i') }}
                </div>
            </div>
            <p class="text-white/70 mb-4">Исследуйте ключевые метрики сети TON, включая активность блокчейна, транзакции
                и рыночные данные.</p>
        </div>

        <!-- Ключевые метрики -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- Высота мастерчейна -->
            <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 shadow-lg transition-all hover:bg-white/15">
                <div class="flex items-center gap-2 mb-2 text-white/70">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                        <path
                            d="M11.644 1.59a.75.75 0 01.712 0l9.75 5.25a.75.75 0 010 1.32l-9.75 5.25a.75.75 0 01-.712 0l-9.75-5.25a.75.75 0 010-1.32l9.75-5.25z"/>
                        <path
                            d="M3.265 10.602l7.668 4.129a2.25 2.25 0 002.134 0l7.668-4.13 1.37.739a.75.75 0 010 1.32l-9.75 5.25a.75.75 0 01-.71 0l-9.75-5.25a.75.75 0 010-1.32l1.37-.738z"/>
                        <path
                            d="M10.933 19.231l-7.668-4.13-1.37.739a.75.75 0 000 1.32l9.75 5.25c.221.12.489.12.71 0l9.75-5.25a.75.75 0 000-1.32l-1.37-.738-7.668 4.13a2.25 2.25 0 01-2.134-.001z"/>
                    </svg>
                    <h2 class="text-lg font-medium">Высота мастерчейна</h2>
                </div>
                <p class="text-3xl font-bold mb-2" id="masterchain_height">
                    {{ number_format($blockStats['latest_masterchain_seqno'])}}
                </p>
                <div class="text-sm text-white/70 flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                        <path fill-rule="evenodd"
                              d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 000-1.5h-3.75V6z"
                              clip-rule="evenodd"/>
                    </svg>
                    Создание блока: {{ $blockStats['stats_1m']['average_block_time'] }}
                </div>
            </div>

            <!-- Количество транзакций -->
            <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 shadow-lg transition-all hover:bg-white/15">
                <div class="flex items-center gap-2 mb-2 text-white/70">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                        <path
                            d="M21.721 12.752a9.711 9.711 0 00-.945-5.003 12.754 12.754 0 01-4.339 2.708 18.991 18.991 0 01-.214 4.772 17.165 17.165 0 005.498-2.477zM14.634 15.55a17.324 17.324 0 00.332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 00.332 4.647 17.385 17.385 0 005.268 0zM9.772 17.119a18.963 18.963 0 004.456 0A17.182 17.182 0 0112 21.724a17.18 17.18 0 01-2.228-4.605zM7.777 15.23a18.87 18.87 0 01-.214-4.774 12.753 12.753 0 01-4.34-2.708 9.711 9.711 0 00-.944 5.004 17.165 17.165 0 005.498 2.477zM21.356 14.752a9.765 9.765 0 01-7.478 6.817 18.64 18.64 0 001.988-4.718 18.627 18.627 0 005.49-2.098zM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 001.988 4.718 9.765 9.765 0 01-7.478-6.816zM13.878 2.43a9.755 9.755 0 016.116 3.986 11.267 11.267 0 01-3.746 2.504 18.63 18.63 0 00-2.37-6.49zM12 2.276a17.152 17.152 0 012.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0112 2.276zM10.122 2.43a18.629 18.629 0 00-2.37 6.49 11.266 11.266 0 01-3.746-2.504 9.754 9.754 0 016.116-3.985z"/>
                    </svg>
                    <h2 class="text-lg font-medium">Количество транзакций</h2>
                </div>
                <p class="text-3xl font-bold mb-2">
                    {{number_format($blockStats['trans_ord_count'])}}
                </p>
                <div class="text-sm text-white/70 flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                        <path fill-rule="evenodd"
                              d="M12 1.5a.75.75 0 01.75.75V4.5a.75.75 0 01-1.5 0V2.25A.75.75 0 0112 1.5zM5.636 4.136a.75.75 0 011.06 0l1.592 1.591a.75.75 0 01-1.061 1.06l-1.591-1.59a.75.75 0 010-1.061zm12.728 0a.75.75 0 010 1.06l-1.591 1.592a.75.75 0 01-1.06-1.061l1.59-1.591a.75.75 0 011.061 0zm-6.816 4.496a.75.75 0 01.82.311l5.228 7.917a.75.75 0 01-.777 1.148l-2.097-.43 1.045 3.9a.75.75 0 01-1.45.388l-1.044-3.899-1.601 1.42a.75.75 0 01-1.247-.606l.569-9.47a.75.75 0 01.554-.68zM3 10.5a.75.75 0 01.75-.75H6a.75.75 0 010 1.5H3.75A.75.75 0 013 10.5zm14.25 0a.75.75 0 01.75-.75h2.25a.75.75 0 010 1.5H18a.75.75 0 01-.75-.75zm-8.962 3.712a.75.75 0 010 1.061l-1.591 1.591a.75.75 0 11-1.061-1.06l1.591-1.592a.75.75 0 011.06 0z"
                              clip-rule="evenodd"/>
                    </svg>
                    {{ round($blockStats['stats_1m']['average_tps'], 2) }} транзакции в секунду
                </div>
            </div>

            <!-- Циркуляция -->
            <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 shadow-lg transition-all hover:bg-white/15">
                <div class="flex items-center gap-2 mb-2 text-white/70">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                        <path d="M12 7.5a2.25 2.25 0 100 4.5 2.25 2.25 0 000-4.5z"/>
                        <path fill-rule="evenodd"
                              d="M1.5 4.875C1.5 3.839 2.34 3 3.375 3h17.25c1.035 0 1.875.84 1.875 1.875v9.75c0 1.036-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 011.5 14.625v-9.75zM8.25 9.75a3.75 3.75 0 117.5 0 3.75 3.75 0 01-7.5 0zM18.75 9a.75.75 0 00-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 00.75-.75V9.75a.75.75 0 00-.75-.75h-.008zM4.5 9.75A.75.75 0 015.25 9h.008a.75.75 0 01.75.75v.008a.75.75 0 01-.75.75H5.25a.75.75 0 01-.75-.75V9.75z"
                              clip-rule="evenodd"/>
                        <path
                            d="M2.25 18a.75.75 0 000 1.5c5.4 0 10.63.722 15.6 2.075 1.19.324 2.4-.558 2.4-1.82V18.75a.75.75 0 00-.75-.75H2.25z"/>
                    </svg>
                    <h2 class="text-lg font-medium">Циркуляция TON</h2>
                </div>
                <p class="text-3xl font-bold mb-2">
                    {{number_format(round($marketStats['circulating_supply']))}} TON
                </p>
                <div class="text-sm text-white/70 flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                        <path fill-rule="evenodd"
                              d="M1.5 5.625c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v12.75c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 011.5 18.375V5.625zM21 9.375A.375.375 0 0020.625 9h-7.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h7.5a.375.375 0 00.375-.375v-1.5zm0 3.75a.375.375 0 00-.375-.375h-7.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h7.5a.375.375 0 00.375-.375v-1.5zm0 3.75a.375.375 0 00-.375-.375h-7.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h7.5a.375.375 0 00.375-.375v-1.5zM10.875 18.75a.375.375 0 00.375-.375v-1.5a.375.375 0 00-.375-.375h-7.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375h7.5zM3.375 15h7.5a.375.375 0 00.375-.375v-1.5a.375.375 0 00-.375-.375h-7.5a.375.375 0 00-.375.375v1.5c0 .207.168.375.375.375zm0-3.75h7.5a.375.375 0 00.375-.375v-1.5A.375.375 0 0010.875 9h-7.5A.375.375 0 003 9.375v1.5c0 .207.168.375.375.375z"
                              clip-rule="evenodd"/>
                    </svg>
                    {{ round(($marketStats['circulating_supply'] / $marketStats['total_supply']) * 100) }}% от общего
                    объема
                </div>
            </div>

            <!-- Общее предложение -->
            <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 shadow-lg transition-all hover:bg-white/15">
                <div class="flex items-center gap-2 mb-2 text-white/70">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                        <path d="M4.5 3.75a3 3 0 00-3 3v.75h21v-.75a3 3 0 00-3-3h-15z"/>
                        <path fill-rule="evenodd"
                              d="M22.5 9.75h-21v7.5a3 3 0 003 3h15a3 3 0 003-3v-7.5zm-18 3.75a.75.75 0 01.75-.75h6a.75.75 0 010 1.5h-6a.75.75 0 01-.75-.75zm.75 2.25a.75.75 0 000 1.5h3a.75.75 0 000-1.5h-3z"
                              clip-rule="evenodd"/>
                    </svg>
                    <h2 class="text-lg font-medium">Общее предложение</h2>
                </div>
                <p class="text-3xl font-bold mb-2" id="total_supply">
                    {{number_format($marketStats['total_supply'])}} TON
                </p>
                <div class="text-sm text-white/70 flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                        <path
                            d="M10.464 8.746c.227-.18.497-.311.786-.394v2.795a2.252 2.252 0 01-.786-.393c-.394-.313-.546-.681-.546-1.004 0-.323.152-.691.546-1.004zM12.75 15.662v-2.824c.347.085.664.228.921.421.427.32.579.686.579.991 0 .305-.152.671-.579.991a2.534 2.534 0 01-.921.42z"/>
                        <path fill-rule="evenodd"
                              d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v.816a3.836 3.836 0 00-1.72.756c-.712.566-1.112 1.35-1.112 2.178 0 .829.4 1.612 1.113 2.178.502.4 1.102.647 1.719.756v2.978a2.536 2.536 0 01-.921-.421l-.879-.66a.75.75 0 00-.9 1.2l.879.66c.533.4 1.169.645 1.821.75V18a.75.75 0 001.5 0v-.81a4.124 4.124 0 001.821-.749c.745-.559 1.179-1.344 1.179-2.191 0-.847-.434-1.632-1.179-2.191a4.122 4.122 0 00-1.821-.75V8.354c.29.082.559.213.786.393l.415.33a.75.75 0 00.933-1.175l-.415-.33a3.836 3.836 0 00-1.719-.755V6z"
                              clip-rule="evenodd"/>
                    </svg>
                    Годовая инфляция: <span id="annual_inflation">{{ round(($marketStats['circulating_supply'] / $marketStats['total_supply']) * 100) }}%</span>
                </div>
            </div>
        </div>

        <!-- График цены TON -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 shadow-lg mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                         class="w-6 h-6 text-primary">
                        <path
                            d="M18.375 2.25c-1.035 0-1.875.84-1.875 1.875v15.75c0 1.035.84 1.875 1.875 1.875h.75c1.035 0 1.875-.84 1.875-1.875V4.125c0-1.036-.84-1.875-1.875-1.875h-.75zM9.75 8.625c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-.75a1.875 1.875 0 01-1.875-1.875V8.625zM3 13.125c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v6.75c0 1.035-.84 1.875-1.875 1.875h-.75A1.875 1.875 0 013 19.875v-6.75z"/>
                    </svg>
                    Активность блоков
                </h2>
                <div class="flex space-x-2">
                    <button id="one_month"
                            class="btn btn-sm bg-white/10 hover:bg-white/20 text-white border-0 rounded-lg">1 месяц
                    </button>
                    <button id="six_months"
                            class="btn btn-sm bg-white/10 hover:bg-white/20 text-white border-0 rounded-lg">6 месяцев
                    </button>
                    <button id="one_year"
                            class="btn btn-sm bg-white/10 hover:bg-white/20 text-white border-0 rounded-lg">1 год
                    </button>
                </div>
            </div>
            <div class="bg-white/5 rounded-lg p-4">
                <canvas id="chart-timeline" class="h-64 w-full"></canvas>
            </div>
        </div>

        <!-- Диаграмма количества адресов -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 shadow-lg mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                         class="w-6 h-6 text-primary">
                        <path fill-rule="evenodd"
                              d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z"
                              clip-rule="evenodd"/>
                    </svg>
                    Количество адресов
                </h2>
                <div class="text-sm text-white/70">
                    Всего адресов: {{ end($totalAccounts)['total_accounts'] ?? 'N/A' }}
                </div>
            </div>
            <div class="bg-white/5 rounded-lg p-4">
                <canvas id="addresses-chart" class="h-64 w-full"></canvas>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // Данные для второго графика (бар)
        const addressesLabels = [
            @foreach($totalAccounts as $totalAccount)
                "{{ date_format(Carbon::createFromTimestamp($totalAccount['timestamp']), 'Y-m-d')}}",
            @endforeach
        ];

        const addressesData = [
            @foreach($totalAccounts as $totalAccount)
                {{ $totalAccount['total_accounts'] }},
            @endforeach
        ];

        const barColors = addressesData.map(value => {
            if (value <= -46) return '#F15B46';
            if (value <= 0) return '#FEB019';
            return '#2d6a4f';
        });

        const barCtx = document.getElementById('addresses-chart').getContext('2d');
        new Chart(barCtx, {
            type: 'bar',
            data: {
                labels: addressesLabels,
                datasets: [{
                    label: 'Количество адресов',
                    data: addressesData,
                    backgroundColor: barColors
                }]
            },
            options: {
                responsive: true,
                scales: {
                    x: {
                        ticks: {
                            maxRotation: 90,
                            minRotation: 90,
                            color: '#a0a0a0'
                        }
                    },
                },
                plugins: {
                    legend: {display: false}
                }
            }
        });

        window.onload = function () {
            const rawPrices = @json($marketData['prices']);
            const labels = rawPrices.map(p => new Date(p[0]).toLocaleDateString());
            const dataPoints = rawPrices.map(p => p[1]);

            const ctx = document.getElementById('chart-timeline').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels,
                    datasets: [{
                        label: 'Ton Price',
                        data: dataPoints,
                        fill: true,
                        backgroundColor: 'rgba(45, 106, 79, 0.1)',
                        borderColor: '#2d6a4f',
                        tension: 0.4,
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            ticks: {
                                color: '#a0a0a0'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#a0a0a0'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        },
                        legend: {
                            display: false
                        }
                    }
                }
            });

            const timestamps = rawPrices.map(p => p[0]);
            const minTimestamp = Math.min(...timestamps);
            const maxTimestamp = Math.max(...timestamps);
            const endDate = new Date(maxTimestamp);

            const resetCssClasses = (activeEl) => {
                document.querySelectorAll('.btn-neutral').forEach(el => el.classList.remove('active'));
                activeEl.target.classList.add('active');
            };

            const zoomChart = (start, end) => {
                const filtered = rawPrices.filter(p => p[0] >= start && p[0] <= end);
                chart.data.labels = filtered.map(p => new Date(p[0]).toLocaleDateString());
                chart.data.datasets[0].data = filtered.map(p => p[1]);
                chart.update();
            };

            document.querySelector('#one_month').addEventListener('click', (e) => {
                resetCssClasses(e);
                const start = new Date(endDate);
                start.setMonth(endDate.getMonth() - 1);
                zoomChart(start.getTime(), endDate.getTime());
            });

            document.querySelector('#six_months').addEventListener('click', (e) => {
                resetCssClasses(e);
                const start = new Date(endDate);
                start.setMonth(endDate.getMonth() - 6);
                zoomChart(start.getTime(), endDate.getTime());
            });

            document.querySelector('#one_year').addEventListener('click', (e) => {
                resetCssClasses(e);
                const start = new Date(endDate);
                start.setFullYear(endDate.getFullYear() - 1);
                zoomChart(start.getTime(), endDate.getTime());
            });
        };
    </script>
@endsection
