@php use Carbon\Carbon; @endphp
@extends('components.layouts.app')

@section('body')
    <div class="container mx-auto px-4 py-8 max-w-7xl text-white">
        <!-- Информация о токене -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 mb-6 shadow-lg">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Основная информация -->
                <div class="md:col-span-2">
                    <div class="flex items-center gap-4 mb-4">
                        <div class="avatar">
                            <div class="w-14 h-14 rounded-full">
                                <img src="{{ $getJetton['jettons'][0]['metadata']['image'] }}" alt="Token Logo">
                            </div>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold flex items-center gap-2">
                                {{ $getJetton['jettons'][0]['metadata']['name'] }}
                                <span
                                    class="badge badge-primary">{{ $getJetton['jettons'][0]['metadata']['symbol'] }}</span>
                            </h1>
                            <div x-data="{
                                copiedToClipboard: false,
                                copyToClipboard() {
                                    navigator.clipboard
                                        .writeText(this.$refs.targetText.textContent.trim())
                                        .then(() => {
                                            this.copiedToClipboard = true;
                                            window.dispatchEvent(new CustomEvent('notify', {
                                                detail: {
                                                    variant: 'success',
                                                    title: 'Успех!',
                                                    message: 'Адрес скопирован'
                                                }
                                            }));
                                            setTimeout(() => { this.copiedToClipboard = false; }, 2000);
                                        });
                                }
                            }" class="flex items-center">
                                <span x-ref="targetText"
                                      class="text-white/60 text-sm font-mono">{{$getJetton['addressBook'][$getJetton['jettons'][0]['metadata']['address']]['userFriendly']}}</span>
                                <button
                                    class="ml-2 rounded-full w-fit p-1 text-neutral-600/75 hover:bg-neutral-950/10 hover:text-neutral-600 focus:outline-hidden focus-visible:text-neutral-600 focus-visible:outline focus-visible:outline-offset-0 focus-visible:outline-black active:bg-neutral-950/5 active:-outline-offset-2 dark:text-neutral-300/75 dark:hover:bg-white/10 dark:hover:text-neutral-300 dark:focus-visible:text-neutral-300 dark:focus-visible:outline-white dark:active:bg-white/5"
                                    title="Copy"
                                    aria-label="Copy"
                                    x-on:click="copyToClipboard()"
                                >
                                    <span class="sr-only"
                                          x-text="copiedToClipboard ? 'скопировано' : 'скопировать адрес'"></span>
                                    <svg x-show="!copiedToClipboard" xmlns="http://www.w3.org/2000/svg"
                                         viewBox="0 0 20 20" fill="currentColor" class="size-4" aria-hidden="true">
                                        <path fill-rule="evenodd"
                                              d="M13.887 3.182c.396.037.79.08 1.183.128C16.194 3.45 17 4.414 17 5.517V16.75A2.25 2.25 0 0 1 14.75 19h-9.5A2.25 2.25 0 0 1 3 16.75V5.517c0-1.103.806-2.068 1.93-2.207.393-.048.787-.09 1.183-.128A3.001 3.001 0 0 1 9 1h2c1.373 0 2.531.923 2.887 2.182ZM7.5 4A1.5 1.5 0 0 1 9 2.5h2A1.5 1.5 0 0 1 12.5 4v.5h-5V4Z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                    <svg x-show="copiedToClipboard" xmlns="http://www.w3.org/2000/svg"
                                         viewBox="0 0 16 16" fill="currentColor" class="size-4 fill-green-500">
                                        <path fill-rule="evenodd"
                                              d="M11.986 3H12a2 2 0 0 1 2 2v6a2 2 0 0 1-1.5 1.937V7A2.5 2.5 0 0 0 10 4.5H4.063A2 2 0 0 1 6 3h.014A2.25 2.25 0 0 1 8.25 1h1.5a2.25 2.25 0 0 1 2.236 2ZM10.5 4v-.75a.75.75 0 0 0-.75-.75h-1.5a.75.75 0 0 0-.75.75V4h3Z"
                                              clip-rule="evenodd"/>
                                        <path fill-rule="evenodd"
                                              d="M2 7a1 1 0 0 1 1-1h7a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7Zm6.585 1.08a.75.75 0 0 1 .336 1.005l-1.75 3.5a.75.75 0 0 1-1.16.234l-1.75-1.5a.75.75 0 0 1 .977-1.139l1.02.875 1.321-2.64a.75.75 0 0 1 1.006-.336Z"
                                              clip-rule="evenodd"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Детали токена -->
                    <div class="bg-white/5 p-4 rounded-lg">
                        <div class="text-sm text-white/70 mb-1">Владелец:</div>
                        @if($getJetton['jettons']['admin']['address'] = '0:0000000000000000000000000000000000000000000000000000000000000000')
                            <div class="text-error">Неизвестен</div>
                        @else
                            <div
                                class="font-medium">{{$getJetton['addressBook'][$getJetton['jettons']['admin']['address']]['userFriendly']}}</div>
                        @endif
                    </div>
                </div>

                <!-- Цена -->
                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="text-sm text-white/70 mb-1">Цена:</div>
                    <div class="text-2xl font-bold">
                        ${{ number_format($getJetton['jettons'][0]['priceUsd']['value'] / pow(10, $getJetton['jettons'][0]['priceUsd']['decimals']), 6) }}
                    </div>
                    <div class="flex items-center gap-2 mt-1">
                        <span
                            class="badge {{ $jettonPriceStats['priceChange']['usd']['day']['changePercent'] >= 0 ? 'badge-success' : 'badge-error' }}">
                            {{ $jettonPriceStats['priceChange']['usd']['day']['changePercent']}}%
                        </span>
                        <span class="text-xs text-white/60">за 24ч</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Блок с графиком -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                             class="w-5 h-5 text-primary">
                            <path fill-rule="evenodd"
                                  d="M3 6a3 3 0 013-3h12a3 3 0 013 3v12a3 3 0 01-3 3H6a3 3 0 01-3-3V6zm4.5 7.5a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0v-2.25a.75.75 0 01.75-.75zm3.75-1.5a.75.75 0 00-1.5 0v4.5a.75.75 0 001.5 0V12zm2.25-3a.75.75 0 01.75.75v6.75a.75.75 0 01-1.5 0V9.75A.75.75 0 0113.5 9zm3.75-1.5a.75.75 0 00-1.5 0v9a.75.75 0 001.5 0v-9z"
                                  clip-rule="evenodd"/>
                        </svg>
                        График цены (7 дней)
                    </h2>
                </div>
                <div class="bg-white/5 p-4 rounded-lg">
                    <canvas id="priceChart" height="80"></canvas>
                </div>
            </div>

            <!-- Блок с деталями токена -->
            <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 shadow-lg">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                             class="w-5 h-5 text-primary">
                            <path fill-rule="evenodd"
                                  d="M2.25 2.25a.75.75 0 000 1.5H3v10.5a3 3 0 003 3h1.21l-1.172 3.513a.75.75 0 001.424.474l.329-.987h8.418l.33.987a.75.75 0 001.422-.474l-1.17-3.513H18a3 3 0 003-3V3.75h.75a.75.75 0 000-1.5H2.25zm6.54 15h6.42l.5 1.5H8.29l.5-1.5zm8.085-8.995a.75.75 0 10-.75-1.299 12.81 12.81 0 00-3.558 3.05L11.03 8.47a.75.75 0 00-1.06 0l-3 3a.75.75 0 101.06 1.06l2.47-2.47 1.617 1.618a.75.75 0 001.146-.102 11.312 11.312 0 013.612-3.321z"
                                  clip-rule="evenodd"/>
                        </svg>
                        Детали токена
                    </h2>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-white/5 p-4 rounded-lg">
                        <div class="text-sm text-white/70 mb-1">Тип контракта:</div>
                        <div class="font-medium">{{ $getJetton['jettons'][0]['type'] ?? '—' }}</div>
                    </div>

                    <div class="bg-white/5 p-4 rounded-lg">
                        <div class="text-sm text-white/70 mb-1">Дата создания:</div>
                        <div
                            class="font-medium">{{ Carbon::parse($getJetton['jettons'][0]['metadata']['createdAt'])->diffForHumans() ?? '—' }}</div>
                    </div>

                    <div class="bg-white/5 p-4 rounded-lg">
                        <div class="text-sm text-white/70 mb-1">Статус проверки:</div>
                        <div class="font-medium">{{$getJetton['jettons'][0]['verification']}}</div>
                    </div>

                    <div class="bg-white/5 p-4 rounded-lg">
                        <div class="text-sm text-white/70 mb-1">Холдеры:</div>
                        <div class="font-medium">{{ number_format($getJetton['jettons'][0]['holdersCount']) }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Статистика -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 shadow-lg mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                         class="w-5 h-5 text-primary">
                        <path
                            d="M18.375 2.25c-1.035 0-1.875.84-1.875 1.875v15.75c0 1.035.84 1.875 1.875 1.875h.75c1.035 0 1.875-.84 1.875-1.875V4.125c0-1.036-.84-1.875-1.875-1.875h-.75zM9.75 8.625c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-.75a1.875 1.875 0 01-1.875-1.875V8.625zM3 13.125c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v6.75c0 1.035-.84 1.875-1.875 1.875h-.75A1.875 1.875 0 013 19.875v-6.75z"/>
                    </svg>
                    Ключевые метрики
                </h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="text-sm text-white/70 mb-1">Капитализация</div>
                    <div class="text-2xl font-bold">
                        ${{ number_format($getJetton['jettons'][0]['fdmc']['value'] / pow(10,$getJetton['jettons'][0]['fdmc']['decimals']), 0) }}
                    </div>
                </div>

                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="text-sm text-white/70 mb-1">Ликвидность</div>
                    <div class="text-2xl font-bold">
                        ${{ number_format($getJetton['jettons'][0]['liquidityUsd']['value'] / pow(10,$getJetton['jettons'][0]['liquidityUsd']['decimals']), 0) }}
                    </div>
                </div>

                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="text-sm text-white/70 mb-1">Холдеры</div>
                    <div class="text-2xl font-bold">{{ number_format($getJetton['jettons'][0]['holdersCount']) }}</div>
                </div>

                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="text-sm text-white/70 mb-1">Доверие рынка</div>
                    <div class="flex items-center gap-2">
                        <input type="range" min="0" max="100" value="{{ $getJetton['jettons'][0]['trustScore'] }}"
                               class="range accent-green-500 cursor-not-allowed w-full" disabled>
                        <span class="text-white font-bold text-sm">{{ $getJetton['jettons'][0]['trustScore'] }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Соц. сети -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 mb-6 shadow-lg">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                         class="w-5 h-5 text-primary">
                        <path fill-rule="evenodd"
                              d="M15.22 6.268a.75.75 0 01.44.97l-2.47 7.5a.75.75 0 01-.97.44l-7.5-2.47a.75.75 0 01-.44-.97l2.47-7.5a.75.75 0 01.97-.44l7.5 2.47zM3.28 3.22a.75.75 0 01.97-.44l7.5 2.47a.75.75 0 01.44.97l-2.47 7.5a.75.75 0 01-.97.44l-7.5-2.47a.75.75 0 01-.44-.97l2.47-7.5z"
                              clip-rule="evenodd"/>
                        <path
                            d="M14.53 9.22a.75.75 0 01.44.97l-2.47 7.5a.75.75 0 01-.97.44l-7.5-2.47a.75.75 0 01-.44-.97l2.47-7.5a.75.75 0 01.97-.44l7.5 2.47z"/>
                    </svg>
                    Официальные ссылки
                </h2>
            </div>
            <div class="flex flex-wrap gap-3">
                @foreach($getJetton['jettons'][0]['metadata']['links'] as $jettonLink)
                    @switch($jettonLink['type'])
                        @case('LINK_TYPE_WEBSITE')
                            <a href="{{ $jettonLink['url'] }}"
                               class="btn btn-sm bg-neutral">
                                {{ $jettonLink['name'] ?? 'Web site' }}
                            </a>
                            @break

                        @case('LINK_TYPE_TELEGRAM')
                            <a href="{{ $jettonLink['url'] }}"
                               class="btn btn-sm bg-[#229ED9] hover:bg-[#1b89ba] text-white border-none">
                                {{ $jettonLink['name'] ?? 'Telegram' }}
                            </a>
                            @break

                        @case('LINK_TYPE_TWITTER')
                            <a href="{{ $jettonLink['url'] }}"
                               class="btn btn-sm bg-neutral-800 hover:bg-neutral-700 text-white border-none">
                                {{ $jettonLink['name'] ?? 'X' }}
                            </a>
                            @break

                        @case('LINK_TYPE_GETGEMS')
                            <a href="{{ $jettonLink['url'] }}"
                               class="btn btn-sm bg-fuchsia-700 hover:bg-fuchsia-800 text-white border-none">
                                {{ $jettonLink['name'] ?? 'GetGems' }}
                            </a>
                            @break

                        @case('LINK_TYPE_DISCORD')
                            <a href="{{ $jettonLink['url'] }}"
                               class="btn btn-sm bg-indigo-700 hover:bg-indigo-800 text-white border-none">
                                {{ $jettonLink['name'] ?? 'Discord' }}
                            </a>
                            @break

                        @case('LINK_TYPE_INSTAGRAM')
                            <a href="{{ $jettonLink['url'] }}"
                               class="btn btn-sm bg-gradient-to-r from-pink-500 via-red-500 to-yellow-500 text-white border-none">
                                {{ $jettonLink['name'] ?? 'Instagram' }}
                            </a>
                            @break

                        @case('LINK_TYPE_OTHER')
                            <a href="{{ $jettonLink['url'] }}"
                               class="btn btn-sm bg-neutral">
                                {{ $jettonLink['name'] ?? 'Other' }}
                            </a>
                            @break
                    @endswitch

                @endforeach
            </div>
        </div>
        <!-- Описание -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 mb-6 shadow-lg">
            <h2 class="text-lg font-semibold mb-2">О токене {{ $jettonData['metadata']['name'] ?? 'N/A' }}</h2>
            <p class="text-white/80 leading-relaxed">
                {{ $jettonData['metadata']['description'] ?? 'Описание отсутствует' }}
            </p>
        </div>

        <!-- Вкладки -->
        <div x-data="{ activeTab: 'swaps' }" class="mb-6">
            <!-- Навигация по вкладкам -->
            <div class="bg-white/10 backdrop-blur-lg rounded-t-xl p-4 flex overflow-x-auto">
                <button
                    @click="activeTab = 'swaps'"
                    :class="activeTab === 'swaps' ? 'bg-white/10 text-white' : 'text-white/70 hover:text-white'"
                    class="px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                        <path
                            d="M21.721 12.752a9.711 9.711 0 00-.945-5.003 12.754 12.754 0 01-4.339 2.708 18.991 18.991 0 01-.214 4.772 17.165 17.165 0 005.498-2.477zM14.634 15.55a17.324 17.324 0 00.332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 00.332 4.647 17.385 17.385 0 005.268 0zM9.772 17.119a18.963 18.963 0 004.456 0A17.182 17.182 0 0112 21.724a17.18 17.18 0 01-2.228-4.605zM7.777 15.23a18.87 18.87 0 01-.214-4.774 12.753 12.753 0 01-4.34-2.708 9.711 9.711 0 00-.944 5.004 17.165 17.165 0 005.498 2.477z"/>
                    </svg>
                    Свапы
                </button>
                <button
                    @click="activeTab = 'transactions'"
                    :class="activeTab === 'transactions' ? 'bg-white/10 text-white' : 'text-white/70 hover:text-white'"
                    class="px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 ml-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                        <path fill-rule="evenodd"
                              d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 000-1.5h-3.75V6z"
                              clip-rule="evenodd"/>
                    </svg>
                    Транзакции
                </button>
                <button
                    @click="activeTab = 'holders'"
                    :class="activeTab === 'holders' ? 'bg-white/10 text-white' : 'text-white/70 hover:text-white'"
                    class="px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 ml-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                        <path fill-rule="evenodd"
                              d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z"
                              clip-rule="evenodd"/>
                    </svg>
                    Держатели
                </button>
            </div>

            <!-- Содержимое вкладок -->
            <div class="bg-white/10 backdrop-blur-lg rounded-b-xl p-6 shadow-lg">
                <!-- Свапы -->
                <div x-show="activeTab === 'swaps'" x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
                    <h2 class="text-xl font-bold mb-4 flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                            <path
                                d="M21.721 12.752a9.711 9.711 0 00-.945-5.003 12.754 12.754 0 01-4.339 2.708 18.991 18.991 0 01-.214 4.772 17.165 17.165 0 005.498-2.477zM14.634 15.55a17.324 17.324 0 00.332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 00.332 4.647 17.385 17.385 0 005.268 0zM9.772 17.119a18.963 18.963 0 004.456 0A17.182 17.182 0 0112 21.724a17.18 17.18 0 01-2.228-4.605zM7.777 15.23a18.87 18.87 0 01-.214-4.774 12.753 12.753 0 01-4.34-2.708 9.711 9.711 0 00-.944 5.004 17.165 17.165 0 005.498 2.477z"/>
                        </svg>
                        Свапы жетона
                    </h2>
                    @include('jetton.parts.swap')
                </div>

                <!-- Транзакции -->
                <div x-show="activeTab === 'transactions'" x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-cloak>
                    <h2 class="text-xl font-bold mb-4 flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                            <path fill-rule="evenodd"
                                  d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 000-1.5h-3.75V6z"
                                  clip-rule="evenodd"/>
                        </svg>
                        Транзакции жетона
                    </h2>
                    @include('wallet.parts.history')
                </div>

                <!-- Держатели -->
                <div x-show="activeTab === 'holders'" x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-cloak>
                    <h2 class="text-xl font-bold mb-4 flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                            <path fill-rule="evenodd"
                                  d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z"
                                  clip-rule="evenodd"/>
                        </svg>
                        Держатели жетона
                    </h2>
                    @include('jetton.parts.holder')
                </div>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const ctx = document.getElementById('priceChart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [
                        @foreach(collect($jettonChart['points'])->reverse() as $chartTime)
                            "{{ Carbon::parse($chartTime['time'])->format('j F') }}",
                        @endforeach
                    ],
                    datasets: [{
                        label: '{{ $getJetton['jettons'][0]['metadata']['symbol'] }} $ Price',
                        data: [

                            @foreach(collect($jettonChart['points'])->reverse() as $chartValue)
                                {{ $chartValue['value']['value'] / pow(10, $chartValue['value']['decimals'])}},
                            @endforeach
                        ],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true,
                    }]
                },
                options: {
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    responsive: true,
                    plugins: {
                        legend: {display: false}
                    },
                    scales: {
                        x: {display: true},
                        y: {beginAtZero: false}
                    }
                }
            });
        });
    </script>

@endsection
