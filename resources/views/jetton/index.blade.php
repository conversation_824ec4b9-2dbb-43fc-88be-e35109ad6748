@php use Carbon\Carbon;use Catchain\Ton\Address\Address; @endphp

@extends('components.layouts.app')

@section('body')
    <div class="container mx-auto px-4 py-8 max-w-7xl text-white">
        <!-- Заголовок страницы -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 mb-6 shadow-lg">
            <div class="flex items-center justify-between mb-4">
                <h1 class="text-2xl md:text-3xl font-bold flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                         class="w-8 h-8 text-primary">
                        <path
                            d="M11.584 2.376a.75.75 0 01.832 0l9 6a.75.75 0 11-.832 1.248L12 3.901 3.416 9.624a.75.75 0 01-.832-1.248l9-6z"/>
                        <path fill-rule="evenodd"
                              d="M20.25 10.332v9.918H21a.75.75 0 010 1.5H3a.75.75 0 010-1.5h.75v-9.918a.75.75 0 01.634-.74A49.109 49.109 0 0112 9c2.59 0 5.134.202 7.616.592a.75.75 0 01.634.74zm-7.5 2.418a.75.75 0 00-1.5 0v6.75a.75.75 0 001.5 0v-6.75zm3-.75a.75.75 0 01.75.75v6.75a.75.75 0 01-1.5 0v-6.75a.75.75 0 01.75-.75zM9 12.75a.75.75 0 00-1.5 0v6.75a.75.75 0 001.5 0v-6.75z"
                              clip-rule="evenodd"/>
                        <path d="M12 7.875a1.125 1.125 0 100-2.25 1.125 1.125 0 000 2.25z"/>
                    </svg>
                    TON Blockchain Tokens
                </h1>
            </div>
            <p class="text-white/70 mb-4">Исследуйте токены в сети TON, отслеживайте цены, рыночную капитализацию и
                другие ключевые метрики.</p>
        </div>

        <!-- Таблица токенов -->
        <livewire:jetton.table lazy/>

    </div>
@endsection
