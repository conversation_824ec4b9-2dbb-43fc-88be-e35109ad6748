@php use Carbon\Carbon;use Catchain\Ton\Address\Address; @endphp

{{-- Обертка для включения горизонтального скролла на всякий случай --}}
<div class="overflow-x-auto">
    <table class="table w-full">
        {{-- Заголовок таблицы - скрыт на мобильных, виден на sm и больше --}}
        <thead class="hidden sm:table-header-group">
        <tr>
            <th>Возраст</th>
            <th>От</th>
            <th>Тип</th>
            <th>Кому</th>
            <th>Сумма</th>
            <th>Статус</th>
        </tr>
        </thead>
        {{-- Тело таблицы - блок на мобильных, таблица на sm и больше --}}
        <tbody class="block sm:table-row-group">
        @foreach($actions['actions'] as $action)
            {{-- Каждая строка - блок на мобильных с границей и отступом, строка таблицы на sm и больше --}}
            <tr class="block border border-base-200 mb-4 sm:table-row sm:border-0">
                {{-- Ячейка Возраст --}}
                <td class="block p-2 sm:table-cell sm:p-4">
                    {{-- Метка для мобильных - видна на мобильных, скрыта на sm и больше --}}
                    <span class="font-bold inline-block w-24 sm:hidden">Возраст:</span>
                    {{ Carbon::createFromTimestamp($action['start_utime'])->diffForHumans() }}
                </td>

                @switch($action['type'])
                    @case('nft_transfer')
                        {{-- Ячейка От (для NFT) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">От:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['old_owner'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['old_owner'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Тип (для NFT) --}}
                        <td class="block p-2 sm:table-cell sm:p-4">
                            <span class="font-bold inline-block w-24 sm:hidden">Тип:</span>
                            <x-action-badge type="nft" label="NFT Transfer"/>
                        </td>

                        {{-- Ячейка Кому (для NFT) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">Кому:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['new_owner'])->toTonscanFormat()])}}">
                                {{  shortMiddle(Address::parse($action['details']['new_owner'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Сумма (для NFT - изображение) --}}
                        <td class="block p-2 sm:table-cell sm:p-4">
                            <span class="font-bold inline-block w-24 sm:hidden">Сумма:</span>
                            <img class="w-12 h-12 rounded-full mx-auto sm:mx-0" {{-- Центрирование на мобильных --}}
                            src="{{ $actions['metadata'][$action['details']['nft_item']]['token_info'][0]['image']
                                           ?? 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSQf9_X2YkV06hyU-QtJDFTE2nAkfA5olUAmA&s' }}"
                                 alt="">
                        </td>
                        @break

                    @case('ton_transfer')
                        {{-- Ячейка От (для TON) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">От:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['source'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['source'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Тип (для TON) --}}
                        <td class="block p-2 sm:table-cell sm:p-4">
                            <span class="font-bold inline-block w-24 sm:hidden">Тип:</span>
                            <x-action-badge type="ton" label="TON Transfer" class="whitespace-nowrap"/>
                        </td>

                        {{-- Ячейка Кому (для TON) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">Кому:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['destination'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['destination'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Сумма (для TON) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono whitespace-nowrap">
                            <span class="font-bold inline-block w-24 sm:hidden">Сумма:</span>
                            {{ number_format($action['details']['value'] / 1000000000, 3) }} TON
                        </td>
                        @break

                    @case('jetton_transfer')
                        {{-- Ячейка От (для Jetton) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">От:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['sender'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['sender'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Тип (для Jetton) --}}
                        <td class="block p-2 sm:table-cell sm:p-4">
                            <span class="font-bold inline-block w-24 sm:hidden">Тип:</span>
                            <x-action-badge type="jetton" label="Jetton Transfer" class="whitespace-nowrap"/>
                        </td>

                        {{-- Ячейка Кому (для Jetton) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">Кому:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['receiver'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['receiver'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Сумма (для Jetton) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono whitespace-nowrap">
                            <span class="font-bold inline-block w-24 sm:hidden">Сумма:</span>
                            {{ number_format($action['details']['amount'] / pow(10, $actions['metadata'][$action['details']['asset']]['token_info'][0]['extra']['decimals'] ?? 9), 3) }}
                            {{ $actions['metadata'][$action['details']['asset']]['token_info'][0]['symbol'] }}
                        </td>
                        @break

                    @case('nft_mint')
                        {{-- Ячейка От (для NFT Mint) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">От:</span>
                            @if(isset($action['details']['nft_collection']))
                                <a class="text-info hover:underline"
                                   href="{{route('search', ['address' => Address::parse($action['details']['nft_collection'])->toTonscanFormat()])}}">
                                    {{ shortMiddle(Address::parse($action['details']['nft_collection'])->toTonscanFormat()) ?? 'N/A' }}
                                </a>
                            @endif
                        </td>

                        {{-- Ячейка Тип (для NFT Mint) --}}
                        <td class="block p-2 sm:table-cell sm:p-4">
                            <span class="font-bold inline-block w-24 sm:hidden">Тип:</span>
                            <x-action-badge type="nft" label="NFT Mint" class="whitespace-nowrap"/>
                        </td>

                        {{-- Ячейка Кому (для NFT Mint) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">Кому:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['owner'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['owner'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Сумма (для NFT Mint - изображение) --}}
                        <td class="block p-2 sm:table-cell sm:p-4">
                            <span class="font-bold inline-block w-24 sm:hidden">Сумма:</span>
                            <img class="w-12 h-12 rounded-full mx-auto sm:mx-0" {{-- Центрирование на мобильных --}}
                            src="{{ $actions['metadata'][$action['details']['nft_item']]['token_info'][0]['image']
                                           ?? 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSQf9_X2YkV06hyU-QtJDFTE2nAkfA5olUAmA&s' }}"
                                 alt="">
                        </td>
                        @break

                    @case('contract_deploy')
                        {{-- Ячейка От (для Deploy) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">От:</span>
                            -
                        </td>

                        {{-- Ячейка Тип (для Deploy) --}}
                        <td class="block p-2 sm:table-cell sm:p-4">
                            <span class="font-bold inline-block w-24 sm:hidden">Тип:</span>
                            <x-action-badge type="deploy" label="Contract Deploy" class="whitespace-nowrap"/>
                        </td>

                        {{-- Ячейка Кому (для Deploy) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">Кому:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['destination'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['destination'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Сумма (для Deploy) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 whitespace-nowrap">
                            <span class="font-bold inline-block w-24 sm:hidden">Сумма:</span>
                            {{number_format($action['details']['value'] / 1000000000, 3). ' TON' ?? 'N / A'}}
                        </td>
                        @break

                    @case('call_contract')
                        {{-- Ячейка От (для Call Contract) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">От:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['source'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['source'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Тип (для Call Contract) --}}
                        <td class="block p-2 sm:table-cell sm:p-4">
                            <span class="font-bold inline-block w-24 sm:hidden">Тип:</span>
                            <x-action-badge type="call" label="Call Contract" class="whitespace-nowrap"/>
                        </td>

                        {{-- Ячейка Кому (для Call Contract) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">Кому:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['destination'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['destination'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Сумма (для Call Contract) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono whitespace-nowrap">
                            <span class="font-bold inline-block w-24 sm:hidden">Сумма:</span>
                            {{ number_format($action['details']['value'] / 1000000000, 3) }} TON
                        </td>
                        @break

                    @case('jetton_swap')
                        {{-- Ячейка От (для Exchange) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">От:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['sender'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['sender'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Тип (для Exchange) --}}
                        <td class="block p-2 sm:table-cell sm:p-4">
                            <span class="font-bold inline-block w-24 sm:hidden">Тип:</span>
                            <x-action-badge type="exchange" label="Exchange" class="whitespace-nowrap"/>
                        </td>

                        {{-- Ячейка Кому (для Exchange) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">Кому:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['dex_outgoing_transfer']['destination'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['dex_outgoing_transfer']['destination'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Сумма (для Exchange) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono whitespace-nowrap">
                            <span class="font-bold inline-block w-24 sm:hidden">Сумма:</span>
                            {{-- Обмен TON на жетон --}}
                            @if($action['details']['dex_incoming_transfer']['asset'] === null)
                                {{ number_format($action['details']['dex_incoming_transfer']['amount'] / 1000000000, 3) }}
                                TON
                                ➡️
                                @if(isset($action['details']['dex_outgoing_transfer']['asset']) && isset($actions['metadata'][$action['details']['dex_outgoing_transfer']['asset']]))
                                    {{ number_format($action['details']['dex_outgoing_transfer']['amount'] / pow(10, $actions['metadata'][$action['details']['dex_outgoing_transfer']['asset']]['token_info'][0]['extra']['decimals'] ?? 9), 3) }}
                                    {{ $actions['metadata'][$action['details']['dex_outgoing_transfer']['asset']]['token_info'][0]['symbol'] ?? '' }}
                                @endif
                                {{-- Обмен жетона на TON --}}
                            @elseif($action['details']['dex_outgoing_transfer']['asset'] === null)
                                @if(isset($action['details']['dex_incoming_transfer']['asset']) && isset($actions['metadata'][$action['details']['dex_incoming_transfer']['asset']]))
                                    {{ number_format($action['details']['dex_incoming_transfer']['amount'] / pow(10, $actions['metadata'][$action['details']['dex_incoming_transfer']['asset']]['token_info'][0]['extra']['decimals'] ?? 9), 3) }}
                                    {{ $actions['metadata'][$action['details']['dex_incoming_transfer']['asset']]['token_info'][0]['symbol'] ?? '' }}
                                @endif
                                ➡️
                                {{ number_format($action['details']['dex_outgoing_transfer']['amount'] / 1000000000, 3) }}
                                TON
                                {{-- Обмен жетона на жетон --}}
                            @else
                                @if(isset($action['details']['dex_incoming_transfer']['asset']) && isset($actions['metadata'][$action['details']['dex_incoming_transfer']['asset']]))
                                    {{ number_format($action['details']['dex_incoming_transfer']['amount'] / pow(10, $actions['metadata'][$action['details']['dex_incoming_transfer']['asset']]['token_info'][0]['extra']['decimals'] ?? 9), 3) }}
                                    {{ $actions['metadata'][$action['details']['dex_incoming_transfer']['asset']]['token_info'][0]['symbol'] ?? '' }}
                                @endif
                                ➡️
                                {{ number_format($action['details']['dex_outgoing_transfer']['amount'] / pow(10, $actions['metadata'][$action['details']['dex_outgoing_transfer']['asset']]['token_info'][0]['extra']['decimals'] ?? 9), 3) }}
                                {{ $actions['metadata'][$action['details']['dex_outgoing_transfer']['asset']]['token_info'][0]['symbol'] ?? '' }}
                            @endif
                        </td>
                        @break

                    @case('auction_bid')
                        {{-- Ячейка От (для Auction Bid) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">От:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['bidder'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['bidder'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Тип (для Auction Bid) --}}
                        <td class="block p-2 sm:table-cell sm:p-4">
                            <span class="font-bold inline-block w-24 sm:hidden">Тип:</span>
                            <x-action-badge type="auction" label="Auction Bid" class="whitespace-nowrap"/>
                        </td>

                        {{-- Ячейка Кому (для Auction Bid) --}}
                        <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                            <span class="font-bold inline-block w-24 sm:hidden">Кому:</span>
                            <a class="text-info hover:underline"
                               href="{{route('search', ['address' => Address::parse($action['details']['auction'])->toTonscanFormat()])}}">
                                {{ shortMiddle(Address::parse($action['details']['auction'])->toTonscanFormat()) ?? 'N/A' }}
                            </a>
                        </td>

                        {{-- Ячейка Сумма (для Auction Bid) --}}
                        <td class="block p-2 sm:table-cell sm:p-4">
                            <span class="font-bold inline-block w-24 sm:hidden">Сумма:</span>
                            <div class="flex items-center gap-2">
                                <img class="w-8 h-8 rounded-full"
                                     src="{{ $actions['metadata'][$action['details']['nft_item']]['token_info'][0]['extra']['_image_small'] ?? $actions['metadata'][$action['details']['nft_item']]['token_info'][0]['image'] ?? 'https://via.placeholder.com/32' }}"
                                     alt="NFT">
                                <div class="font-mono whitespace-nowrap">
                                    {{ number_format($action['details']['amount'] / 1000000000, 3) }} TON
                                </div>
                            </div>
                        </td>
                        @break
                @endswitch

                {{-- Ячейка Статус --}}
                <td class="block p-2 sm:table-cell sm:p-4">
                    <span class="font-bold inline-block w-24 sm:hidden">Статус:</span>
                    @if($action['success'] === true)
                        <div class="badge badge-success">Success</div>
                    @else
                        <div class="badge badge-error">Error</div>
                    @endif
                </td>
            </tr>
        @endforeach
        </tbody>
    </table>
</div>
