@php use Catchain\Ton\Address\Address; @endphp
<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
    @foreach($nfts['nft_items'] as $nftItem)
        <a href="{{route('nft.show', ['address' => Address::parse($nftItem['address'])->toTonscanFormat()])}}">
            <div class="card bg-white/10 backdrop-blur-lg shadow-xl border border-white/10 rounded-xl">
                <figure class="px-4 pt-4">
                    <img
                        src="{{ isset($nfts['metadata'][$nftItem['address']]['token_info'][0]['extra']) ? ($nfts['metadata'][$nftItem['address']]['token_info'][0]['extra']['_image_medium'] ?? domainToImage($nfts['metadata'][$nftItem['address']]['token_info'][0]['extra']['domain'] ?? null) ?? asset('images/nft-placeholder.png')) : asset('images/nft-placeholder.png') }}"
                        alt="NFT Image"
                        class="rounded-xl h-48 object-cover w-full"/>
                </figure>
                <div class="card-body text-white/90">
                    <h2 class="card-title text-base font-semibold">
                        {{ $nfts['metadata'][$nftItem['address']]['token_info'][0]['name'] ?? 'N / A' }}
                    </h2>
                    <p class="text-sm text-white/70">{{ $nfts['metadata'][$nftItem['collection_address']]['token_info'][0]['name'] ?? 'N / A'}}</p>
                </div>
            </div>
            @endforeach
        </a>
</div>
