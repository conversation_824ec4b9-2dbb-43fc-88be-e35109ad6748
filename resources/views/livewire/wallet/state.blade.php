@php $account = $accounts[0]; @endphp
<div class="container mx-auto px-4 sm:px-6 py-8 max-w-7xl">
    <!-- Информация о кошельке -->
    <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 mb-6 shadow-lg">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Основная информация -->
            <div class="md:col-span-2">
                <div class="flex items-center gap-2 mb-4">
                    <h1 class="text-2xl md:text-3xl font-bold flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                             class="w-8 h-8 text-primary">
                            <path
                                d="M2.273 5.625A4.483 4.483 0 015.25 4.5h13.5c1.141 0 2.183.425 2.977 1.125A3 3 0 0018.75 3H5.25a3 3 0 00-2.977 2.625zM2.273 8.625A4.483 4.483 0 015.25 7.5h13.5c1.141 0 2.183.425 2.977 1.125A3 3 0 0018.75 6H5.25a3 3 0 00-2.977 2.625zM5.25 9a3 3 0 00-3 3v6a3 3 0 003 3h13.5a3 3 0 003-3v-6a3 3 0 00-3-3H15a.75.75 0 00-.75.75 2.25 2.25 0 01-4.5 0A.75.75 0 009 9H5.25z"/>
                        </svg>
                        Кошелек
                    </h1>
                    <div class="flex flex-wrap gap-2">
                        <div
                            class="badge badge-success">{{ ucfirst($walletStates['wallets'][0]['status']) }}</div>
                        @if(isset($walletStates['wallets'][0]['wallet_type']))
                            <div
                                class="badge badge-neutral">{{ ucfirst($walletStates['wallets'][0]['wallet_type']) }}</div>
                        @endif
                    </div>
                </div>

                <!-- Адрес кошелька -->
                <div class="bg-white/5 p-4 rounded-lg mb-4">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-white/70 mb-1">Адрес:</div>
                        <button
                            class="rounded-full w-fit p-1 text-neutral-600/75 hover:bg-neutral-950/10 hover:text-neutral-600 focus:outline-hidden focus-visible:text-neutral-600 focus-visible:outline focus-visible:outline-offset-0 focus-visible:outline-black active:bg-neutral-950/5 active:-outline-offset-2 dark:text-neutral-300/75 dark:hover:bg-white/10 dark:hover:text-neutral-300 dark:focus-visible:text-neutral-300 dark:focus-visible:outline-white dark:active:bg-white/5"
                            title="Copy"
                            aria-label="Copy"
                            x-on:click="copyToClipboard()"
                        >
                                        <span class="sr-only"
                                              x-text="copiedToClipboard ? 'скопировано' : 'скопировать адрес'"></span>
                            <svg x-show="!copiedToClipboard" xmlns="http://www.w3.org/2000/svg"
                                 viewBox="0 0 20 20" fill="currentColor" class="size-5" aria-hidden="true">
                                <path fill-rule="evenodd"
                                      d="M13.887 3.182c.396.037.79.08 1.183.128C16.194 3.45 17 4.414 17 5.517V16.75A2.25 2.25 0 0 1 14.75 19h-9.5A2.25 2.25 0 0 1 3 16.75V5.517c0-1.103.806-2.068 1.93-2.207.393-.048.787-.09 1.183-.128A3.001 3.001 0 0 1 9 1h2c1.373 0 2.531.923 2.887 2.182ZM7.5 4A1.5 1.5 0 0 1 9 2.5h2A1.5 1.5 0 0 1 12.5 4v.5h-5V4Z"
                                      clip-rule="evenodd"/>
                            </svg>
                            <svg x-show="copiedToClipboard" xmlns="http://www.w3.org/2000/svg"
                                 viewBox="0 0 16 16" fill="currentColor" class="size-4 fill-green-500">
                                <path fill-rule="evenodd"
                                      d="M11.986 3H12a2 2 0 0 1 2 2v6a2 2 0 0 1-1.5 1.937V7A2.5 2.5 0 0 0 10 4.5H4.063A2 2 0 0 1 6 3h.014A2.25 2.25 0 0 1 8.25 1h1.5a2.25 2.25 0 0 1 2.236 2ZM10.5 4v-.75a.75.75 0 0 0-.75-.75h-1.5a.75.75 0 0 0-.75.75V4h3Z"
                                      clip-rule="evenodd"/>
                                <path fill-rule="evenodd"
                                      d="M2 7a1 1 0 0 1 1-1h7a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7Zm6.585 1.08a.75.75 0 0 1 .336 1.005l-1.75 3.5a.75.75 0 0 1-1.16.234l-1.75-1.5a.75.75 0 0 1 .977-1.139l1.02.875 1.321-2.64a.75.75 0 0 1 1.006-.336Z"
                                      clip-rule="evenodd"/>
                            </svg>
                        </button>
                    </div>
                    <div class="font-mono text-sm break-all"
                         x-ref="targetText">{{ $addressBook[$account['address']]['user_friendly'] }}</div>
                </div>
                <!-- Баланс -->
                <div class="bg-white/5 p-4 rounded-lg">
                    <div class="text-sm text-white/70 mb-1">Баланс TON:</div>
                    <div class="flex flex-col">
                        <div
                            class="text-2xl font-bold">{{ number_format($account['balance'] / **********, 5) }}</div>
                        <div class="text-sm text-white/60">≈
                            ${{ number_format(($account['balance'] / **********) * tonPrice(), 2) }}</div>
                    </div>
                </div>
            </div>

            <!-- QR код -->
            <div class="flex flex-col items-center justify-center bg-white/5 p-4 rounded-lg">
                <div class="text-sm text-white/70 mb-2">Отсканируйте QR-код для перевода</div>
                <img
                    src="{{ qrCodeGenerate($addressBook[$account['address']]['user_friendly']) }}"
                    alt="QR Code"
                    class="rounded-lg shadow-lg w-full max-w-[200px] cursor-pointer transition hover:scale-105"
                    x-on:click="modalIsOpen = true"
                >
                <div class="text-xs text-white/50 mt-2">Нажмите на QR-код для увеличения</div>
            </div>
        </div>
    </div>

    {{-- Модальное окно --}}
    
</div>
