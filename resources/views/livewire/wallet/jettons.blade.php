@php use Carbon\Carbon;use Catchain\Ton\Address\Address; @endphp

{{-- Обертка для включения горизонтального скролла --}}
<div class="overflow-x-auto">
    <table class="table w-full">
        {{-- Заголовок таблицы - скрыт на мобильных, виден на sm и больше --}}
        <thead class="hidden sm:table-header-group">
        <tr>
            <th>Jetton</th>
            <th>Balance</th>
            <th>Jetton wallet</th>
        </tr>
        </thead>
        {{-- Тело таблицы - блок на мобильных, таблица на sm и больше --}}
        <tbody class="block sm:table-row-group">
        @foreach($jettonWallets as $jettonItem)
            {{-- Каждая строка - блок на мобильных с границей и отступом, строка таблицы на sm и больше --}}
            <tr class="block border border-base-200 mb-4 sm:table-row sm:border-0 hover">
                {{-- Ячейка Jetton --}}
                <td class="block p-2 sm:table-cell sm:p-4">
                    {{-- Метка для мобильных - видна на мобильных, скрыта на sm и больше --}}
                    <span class="font-bold inline-block w-32 sm:hidden">Jetton:</span> {{-- Увеличил ширину метки --}}
                    {{-- Сохраняем flex для выравнивания содержимого ячейки на всех размерах --}}
                    <div class="flex items-center gap-3">
                        <img src="{{$jettons['metadata'][$jettonItem['jetton']]['token_info'][0]['image'] ?? 'N / A'}}"
                             alt="Jetton Logo" class="w-10 h-10 rounded-full object-cover">
                        <div>
                            <a href="{{route('jettons.show', ['address' => Address::parse($jettonItem['jetton'])->toTonscanFormat() ])}}"
                               class="hover:text-primary transition-colors">
                                <div
                                    class="font-semibold text-sm">{{$jettons['metadata'][$jettonItem['jetton']]['token_info'][0]['name'] ?? 'N/A'}}</div>
                                <div
                                    class="text-xs">{{$jettons['metadata'][$jettonItem['jetton']]['token_info'][0]['symbol'] ?? 'N/A'}}</div>
                            </a>
                        </div>
                    </div>
                </td>
                {{-- Ячейка Balance --}}
                <td class="block p-2 sm:table-cell sm:p-4 font-mono">
                    <span class="font-bold inline-block w-32 sm:hidden">Balance:</span>
                    {{$jettonItem['balance'] / pow(10,$jettons['metadata'][$jettonItem['jetton']]['token_info'][0]['extra']['decimals'] ?? 9   )}}
                    {{$jettons['metadata'][$jettonItem['jetton']]['token_info'][0]['symbol'] ?? 'N/A'}}
                </td>
                {{-- Ячейка Jetton wallet --}}
                <td class="block p-2 sm:table-cell sm:p-4 text-sm ">
                    <span class="font-bold inline-block w-32 sm:hidden">Jetton wallet:</span>
                    {{$jettons['address_book'][$jettonItem['address']]['domain'] ?? $jettons['address_book'][$jettonItem['address']]['user_friendly']}}
                </td>
            </tr>
        @endforeach
        </tbody>
    </table>
</div>
