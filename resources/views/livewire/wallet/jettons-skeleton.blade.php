<div class="overflow-x-auto">
    <div class="animate-pulse bg-white/10 backdrop-blur-lg rounded-xl p-4 shadow">
        <table class="table w-full">
            <!-- Заголовок -->
            <thead class="hidden sm:table-header-group">
            <tr class="text-white/70 border-b border-white/10">
                <th>Jetton</th>
                <th>Balance</th>
                <th>Jetton wallet</th>
            </tr>
            </thead>
            <!-- Skeleton строки -->
            <tbody class="block sm:table-row-group">
            @for($i = 0; $i < 6; $i++)
                <tr class="block border border-white/10 rounded-lg mb-4 sm:table-row sm:border-0 sm:border-b">
                    <!-- Jetton -->
                    <td class="block p-2 sm:table-cell sm:p-4">
                        <span class="font-bold inline-block w-32 sm:hidden">Jetton:</span>
                        <div class="flex items-center gap-3">
                            <div class="skeleton w-10 h-10 rounded-full bg-white/20"></div>
                            <div class="space-y-1">
                                <div class="skeleton w-24 h-4 bg-white/20 rounded"></div>
                                <div class="skeleton w-16 h-3 bg-white/10 rounded"></div>
                            </div>
                        </div>
                    </td>
                    <!-- Balance -->
                    <td class="block p-2 sm:table-cell sm:p-4">
                        <span class="font-bold inline-block w-32 sm:hidden">Balance:</span>
                        <div class="skeleton w-28 h-4 bg-white/20 rounded font-mono"></div>
                    </td>
                    <!-- Jetton wallet -->
                    <td class="block p-2 sm:table-cell sm:p-4">
                        <span class="font-bold inline-block w-32 sm:hidden">Jetton wallet:</span>
                        <div class="skeleton w-48 h-4 bg-white/20 rounded"></div>
                    </td>
                </tr>
            @endfor
            </tbody>
        </table>
    </div>
</div>
