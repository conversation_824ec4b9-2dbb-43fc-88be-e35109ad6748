<?php

namespace App\Http\Controllers;


use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class WalletController extends Controller
{
    /**
     * @throws ConnectionException
     */
    public function show($address)
    {
        $response = Http::toncenter()
            ->withQueryParameters(['address' => $address])
            ->async()
            ->get('accountStates')
            ->wait();

        $accounts = $response['accounts'];

        return view('wallet.show', compact('response', 'accounts', 'address'));
    }

    public function welcome()
    {
        return view('index.welcome');
    }

    public function topAccount()
    {
        return view('topAccount.index');
    }
}
