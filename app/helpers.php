<?php

use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\WebPWriter;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

if (!function_exists('shortMiddle')) {
    function shortMiddle(?string $string, int $start = 18, int $end = 8): string
    {
        if (!$string || strlen($string) <= ($start + $end + 3)) return $string;
        return substr($string, 0, $start) . '...' . substr($string, -$end);
    }
}

if (!function_exists('qrCodeGenerate')) {
    function qrCodeGenerate(string $address): string
    {
        $qrCode = new QrCode('ton://transfer/' . $address);
        $writer = new WebPWriter();
        $result = $writer->write($qrCode);
        header('Content-type:' . $result->getMimeType());
        return $result->getDataUri();
    }

}

if (!function_exists('domainToImage')) {
    function domainToImage(?string $domain): string
    {
        if (!$domain) {
            return 'https://via.placeholder.com/400x400.png?text=No+Domain';
        }
        return 'https://dns-image.mytonwallet.org/img?d=' . str_replace('.ton', '', $domain);
    }
}

if (!function_exists('tonPrice')) {
    /**
     * @throws ConnectionException
     */
    function tonPrice(): float
    {
        return Http::get('https://api.ton.cat/v2/contracts/blockchain/market_stats')['quotes']['usd']['price'];
    }
}

