<?php

namespace App\Livewire\Index;

use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Livewire\Component;

class Stats extends Component
{
    /**
     * @throws ConnectionException
     */
    public $marketStats;

    public function mount()
    {
        $this->marketStats = Http::retry(5, 100)
            ->get('https://api.ton.cat/v2/contracts/blockchain/market_stats')
            ->collect();
    }

    public function placeholder()
    {
        return view('livewire.index.stats-skeleton');
    }

    public function render()
    {
        return view('livewire.index.stats');
    }
}
