<?php

namespace App\Livewire\TopAccount;

use Illuminate\Support\Facades\Http;
use Livewire\Component;

class Table extends Component
{
    public $topAccounts;

    public function mount()
    {
        $this->topAccounts = Http::retry(5, 100)
            ->withQueryParameters([
                'limit' => 25])
            ->get('https://toncenter.com/api/v3/topAccountsByBalance')
            ->collect();
    }

    public function placeholder()
    {
        return view('livewire.top-account.table-skeleton');
    }

    public function render()
    {
        return view('livewire.top-account.table');
    }
}
