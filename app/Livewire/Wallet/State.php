<?php

namespace App\Livewire\Wallet;

use Illuminate\Support\Facades\Http;
use Livewire\Component;

class State extends Component
{
    public $walletStates;

    public $response;

    public function mount($address)
    {
        $this->response = Http::toncenter()
            ->withQueryParameters(['address' => $address])
            ->async()
            ->get('accountStates')
            ->wait();

        $this->walletStates = Http::toncenter()
            ->withQueryParameters(['address' => $address])
            ->async()
            ->get('walletStates')
            ->wait();
    }

    public function placeholder()
    {
        return view('livewire.wallet.state-skeleton');
    }

    public function render()
    {
        $accounts = $this->response['accounts'];
        $addressBook = $this->response['address_book'];
        return view('livewire.wallet.state', compact('accounts', 'addressBook'));
    }
}
