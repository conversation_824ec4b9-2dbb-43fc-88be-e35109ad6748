<?php

namespace App\Livewire\Wallet;

use Illuminate\Support\Facades\Http;
use Livewire\Component;

class Jettons extends Component
{

    public $jettons;

    public function mount($address)
    {
        $this->jettons = Http::toncenter()
            ->retry(5, 100)
            ->withQueryParameters([
                'owner_address' => $address,
                'limit' => 20,
                'exclude_zero_balance' => true,])
            ->async()
            ->get('jetton/wallets')
            ->wait()
            ->collect();
    }

    public function placeholder()
    {
        return view('livewire.wallet.jettons-skeleton');
    }

    public function render()
    {
        $jettonWallets = $this->jettons['jetton_wallets'] ?? [];

        return view('livewire.wallet.jettons', compact('jettonWallets'));
    }

}
