<?php

namespace App\Livewire\Wallet;

use Illuminate\Support\Facades\Http;
use Livewire\Component;

class Nfts extends Component
{
    public $nfts;

    public function mount($address)
    {
        $this->nfts = Http::toncenter()
            ->retry(5, 100)
            ->withQueryParameters([
                'owner_address' => $address,
                'limit' => 20])
            ->async()
            ->get('nft/items')
            ->wait()
            ->collect();
    }

    public function placeholder()
    {
        return view('livewire.wallet.nfts-skeleton');
    }
    
    public function render()
    {
        return view('livewire.wallet.nfts', [
            'nfts' => $this->nfts,
        ]);
    }

}
