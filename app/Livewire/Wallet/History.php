<?php

namespace App\Livewire\Wallet;

use Illuminate\Support\Facades\Http;
use Livewire\Component;

class History extends Component
{

    public $response;

    public $walletStates;

    public $actions;

    public $jettons;

    public $nfts;

    public function mount($address)
    {
        $this->response = Http::toncenter()
            ->retry(5, 100)
            ->withQueryParameters(['address' => $address])
            ->async()
            ->get('accountStates')
            ->wait();

        $this->walletStates = Http::toncenter()
            ->retry(5, 100)
            ->withQueryParameters(['address' => $address])
            ->async()
            ->get('walletStates')
            ->wait();

        $this->actions = Http::toncenter()
            ->retry(5, 100)
            ->withQueryParameters([
                'account' => $address,
                'limit' => 20,
                'sort' => 'desc'])
            ->async()
            ->get('actions')
            ->wait()
            ->collect();

        $this->jettons = Http::toncenter()
            ->retry(5, 100)
            ->withQueryParameters([
                'owner_address' => $address,
                'limit' => 20,
                'exclude_zero_balance' => true,])
            ->async()
            ->get('jetton/wallets')
            ->wait()
            ->collect();

        $this->nfts = Http::toncenter()
            ->retry(5, 100)
            ->withQueryParameters([
                'owner_address' => $address,
                'limit' => 20])
            ->async()
            ->get('nft/items')
            ->wait()
            ->collect();
    }

    public function placeholder()
    {
        return view('livewire.wallet.history-skeleton');
    }

    public function render()
    {
        $jettonWallets = $this->jettons['jetton_wallets'];
        $accounts = $this->response['accounts'];
        $addressBook = $this->response['address_book'];

        return view('livewire.wallet.history', compact('jettonWallets', 'accounts', 'addressBook'));
    }

}
